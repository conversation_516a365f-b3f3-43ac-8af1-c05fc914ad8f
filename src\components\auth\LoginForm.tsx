"use client";

import React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Google } from "@/components/icons/Google";
import { useAuthForm } from "@/hooks/useAuthForm";
import { useGoogleAuth } from "@/hooks/useGoogleAuth";

export function LoginForm() {
  const {
    formData,
    formErrors,
    isLoading,
    error,
    success,
    handleInputChange,
    handleLogin,
  } = useAuthForm();

  const { signInWithGoogle, isLoading: googleLoading } = useGoogleAuth();

  return (
    <div className="flex min-h-screen w-full items-center justify-center p-4 lg:min-h-0 lg:p-8">
      <div className="w-full max-w-[440px] space-y-8 rounded-2xl bg-[#0F1128]/40 p-4 backdrop-blur-sm transition-all duration-500 hover:bg-[#0F1128]/50 sm:p-6 md:p-8">
        <div
          className="animate-fade-in-up space-y-2 text-center"
          style={{
            animationDelay: "0.1s",
          }}
        >
          <h1 className="bg-gradient-to-r from-white to-gray-400 bg-clip-text text-3xl font-bold tracking-tight text-transparent sm:text-4xl">
            Chào mừng trở lại
          </h1>
          <p className="text-sm text-gray-400 sm:text-base">
            Chúng tôi rất vui mừng được gặp lại bạn!
          </p>
        </div>

        {/* Success/Error Messages */}
        {(error || success) && (
          <div
            className="animate-fade-in-up"
            style={{ animationDelay: "0.15s" }}
          >
            {error && (
              <div className="rounded-xl border border-red-500/20 bg-red-500/10 p-3 text-sm text-red-400">
                {error}
              </div>
            )}
            {success && (
              <div className="rounded-xl border border-green-500/20 bg-green-500/10 p-3 text-sm text-green-400">
                {success}
              </div>
            )}
          </div>
        )}

        <div
          className="animate-fade-in-up space-y-4 sm:space-y-6"
          style={{
            animationDelay: "0.2s",
          }}
        >
          <Button
            variant="outline"
            onClick={signInWithGoogle}
            disabled={isLoading || googleLoading}
            className="group relative w-full overflow-hidden rounded-xl border-[#2D2F53] bg-[#161837]/50 py-5 text-sm text-gray-300 transition-all duration-300 hover:border-[#3D4073] hover:bg-[#1A1D40]/50 hover:text-white disabled:cursor-not-allowed disabled:opacity-50 sm:py-6 sm:text-base"
          >
            <div className="relative z-10 flex items-center justify-center">
              <Google className="mr-2 h-4 w-4 transition-transform duration-300 group-hover:scale-110" />
              {googleLoading ? "Đang đăng nhập..." : "Đăng nhập với Google"}
            </div>
          </Button>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-[#2D2F53]" />
            </div>
            <div className="relative flex justify-center text-xs sm:text-sm">
              <span className="bg-[#0F1128]/40 px-4 text-gray-500">
                Hoặc tiếp tục với email
              </span>
            </div>
          </div>
        </div>
        <form
          onSubmit={handleLogin}
          className="animate-fade-in-up space-y-6"
          style={{
            animationDelay: "0.3s",
          }}
        >
          <div className="space-y-4 sm:space-y-5">
            <div className="space-y-1 sm:space-y-2">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-300"
              >
                Email
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className={`mt-1 block w-full rounded-xl border-[#2D2F53] bg-[#161837]/50 text-white placeholder:text-gray-500 focus-within:ring-0 focus-within:ring-offset-0 hover:border-[#3D4073] focus:ring-0 focus:ring-offset-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 ${
                  formErrors.email ? "border-red-500/50" : ""
                }`}
                placeholder="Nhập email của bạn"
              />
              {formErrors.email && (
                <p className="text-sm text-red-400">{formErrors.email}</p>
              )}
            </div>
            <div className="space-y-1 sm:space-y-2">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-300"
              >
                Mật khẩu
              </label>
              <Input
                id="password"
                name="password"
                type="password"
                autoComplete="current-password"
                required
                value={formData.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                className={`mt-1 block w-full rounded-xl border-[#2D2F53] bg-[#161837]/50 text-white placeholder:text-gray-500 focus-within:ring-0 focus-within:ring-offset-0 hover:border-[#3D4073] focus:ring-0 focus:ring-offset-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 ${
                  formErrors.password ? "border-red-500/50" : ""
                }`}
                placeholder="Nhập mật khẩu của bạn"
              />
              {formErrors.password && (
                <p className="text-sm text-red-400">{formErrors.password}</p>
              )}
            </div>
          </div>
          <div className="flex flex-col space-y-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <label className="relative inline-flex cursor-pointer items-center">
              <input
                type="checkbox"
                id="remember-me"
                name="remember-me"
                checked={formData.rememberMe}
                onChange={(e) =>
                  handleInputChange("rememberMe", e.target.checked)
                }
                className="peer sr-only"
              />
              <div className="relative h-5 w-9 rounded-full border border-[#2D2F53] bg-[#161837]/50 peer-checked:border-[#8B5CF6]/50 peer-checked:bg-[#8B5CF6]/20 after:absolute after:top-1/2 after:left-[2px] after:h-4 after:w-4 after:-translate-y-1/2 after:rounded-full after:border after:border-[#2D2F53] after:bg-gray-500/50 after:shadow-sm after:transition-all after:duration-200 after:content-[''] peer-checked:after:translate-x-4 peer-checked:after:border-[#8B5CF6] peer-checked:after:bg-[#8B5CF6] hover:border-[#3D4073] peer-checked:hover:border-[#7C3AED]/50 hover:after:bg-gray-400/50 peer-checked:hover:after:border-[#7C3AED] peer-checked:hover:after:bg-[#7C3AED]"></div>
              <span className="ml-2.5 text-sm text-gray-300">Ghi nhớ tôi</span>
            </label>
            <div className="text-sm">
              <a
                href="/auth/forgot-password"
                className="font-medium text-[#8B5CF6] transition-colors duration-300 hover:text-[#7C3AED]"
              >
                Quên mật khẩu?
              </a>
            </div>
          </div>
          <Button
            type="submit"
            disabled={isLoading}
            className="group relative w-full overflow-hidden rounded-xl bg-gradient-to-r from-[#8B5CF6] to-[#7C3AED] py-5 text-sm text-white transition-all duration-300 hover:from-[#7C3AED] hover:to-[#6D28D9] disabled:cursor-not-allowed disabled:opacity-50 sm:py-6 sm:text-base"
          >
            <div className="relative z-10 flex items-center justify-center transition-transform duration-300 group-hover:scale-105">
              {isLoading ? "Đang đăng nhập..." : "Đăng nhập"}
            </div>
          </Button>
        </form>
        <div
          className="animate-fade-in-up"
          style={{
            animationDelay: "0.4s",
          }}
        >
          <p className="text-center text-xs text-gray-500 sm:text-sm">
            Bằng cách đăng ký, bạn đồng ý với{" "}
            <a
              href="#"
              className="text-[#8B5CF6] transition-colors duration-300 hover:text-[#7C3AED]"
            >
              Điều khoản Dịch vụ
            </a>{" "}
            và{" "}
            <a
              href="#"
              className="text-[#8B5CF6] transition-colors duration-300 hover:text-[#7C3AED]"
            >
              Chính sách Bảo mật
            </a>
          </p>
          <div className="flex items-center justify-center space-x-2 pt-2 text-sm sm:pt-3">
            <span className="text-gray-400">Chưa có tài khoản?</span>
            <Link
              href="/signup"
              className="font-medium text-[#8B5CF6] transition-colors duration-300 hover:text-[#7C3AED]"
            >
              Đăng ký ngay
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

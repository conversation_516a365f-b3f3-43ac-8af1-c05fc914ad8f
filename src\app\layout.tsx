import type { <PERSON><PERSON><PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/context/AuthContext";

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: "swap",
});

export const metadata: Metadata = {
  title: "NextApp - Build Amazing Web Experiences",
  description:
    "Create fast, scalable, and modern web applications with the power of React and Next.js. Get started in minutes with our comprehensive platform.",
  keywords: [
    "Next.js",
    "React",
    "Web Development",
    "Modern Apps",
    "TypeScript",
  ],
  authors: [{ name: "NextApp Team" }],
  creator: "NextApp",
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://nextapp.com",
    title: "NextApp - Build Amazing Web Experiences",
    description:
      "Create fast, scalable, and modern web applications with the power of React and Next.js.",
    siteName: "NextApp",
  },
  twitter: {
    card: "summary_large_image",
    title: "NextApp - Build Amazing Web Experiences",
    description:
      "Create fast, scalable, and modern web applications with the power of React and Next.js.",
    creator: "@nextapp",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${inter.variable} antialiased`}>
        <AuthProvider>{children}</AuthProvider>
      </body>
    </html>
  );
}

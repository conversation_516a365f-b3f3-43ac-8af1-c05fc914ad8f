"use client";

import { useCallback, useState } from "react";
import { createClient } from "@/lib/supabase/client";
import { AuthError } from "@supabase/supabase-js";

export function useGoogleAuth() {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const supabase = createClient();

  const signInWithGoogle = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        throw error;
      }
    } catch (error) {
      const message =
        error instanceof AuthError
          ? error.message
          : "Đăng nhập với Google thất bại";
      setError(message);
      setIsLoading(false);
    }
  }, [supabase.auth]);

  return {
    signInWithGoogle,
    isLoading,
    error,
  };
}

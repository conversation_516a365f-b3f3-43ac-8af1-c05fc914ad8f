"use client";

import { Testimonials } from "@/components/auth/Testimonials";

export default function AuthLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <main className="min-h-screen w-full bg-[#0A0B1A] bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-[#0A0B1A] via-[#0A0B1A] to-[#0F1128]">
      <div className="container mx-auto grid min-h-screen lg:grid-cols-2">
        <div className="relative flex items-center justify-center">
          <div className="w-full">
            {children}
          </div>
        </div>
        <Testimonials />
      </div>
    </main>
  );
}

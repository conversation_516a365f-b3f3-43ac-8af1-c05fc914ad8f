"use client";

import { useAuth } from "@/hooks/useAuth";
import { LogOut } from "lucide-react";

interface LogoutButtonProps {
  variant?: "button" | "link";
  className?: string;
}

export function LogoutButton({
  variant = "button",
  className = "",
}: LogoutButtonProps) {
  const { signOut, isLoading } = useAuth();

  const handleLogout = async () => {
    await signOut();
  };

  if (variant === "link") {
    return (
      <button
        onClick={handleLogout}
        disabled={isLoading}
        className={`flex items-center text-sm text-gray-600 hover:text-gray-900 disabled:opacity-50 ${className}`}
      >
        <LogOut className="mr-2 h-4 w-4" />
        {isLoading ? "Signing out..." : "Sign out"}
      </button>
    );
  }

  return (
    <button
      onClick={handleLogout}
      disabled={isLoading}
      className={`flex items-center justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
    >
      <LogOut className="mr-2 h-4 w-4" />
      {isLoading ? "Signing out..." : "Sign out"}
    </button>
  );
}

/* eslint-disable @next/next/no-img-element */
import React from "react";
export function Testimonials() {
  return <div className="hidden w-full flex-col justify-between p-4 lg:flex lg:p-8">
      <div className="flex flex-1 items-center justify-center">
        <blockquote className="space-y-2">
          <p className="text-base text-gray-300 sm:text-lg md:text-xl lg:text-2xl">
            &ldquo;Chúng tôi yêu thích ThueGPU và nó đã tạo ra tác động lớn đến tốc độ
            phát triển của chúng tôi.&rdquo;
          </p>
          <footer className="text-xs text-gray-500 sm:text-sm md:text-base">
            Tan Khoa, Giám đốc Công nghệ
          </footer>
        </blockquote>
      </div>
      <div className="space-y-4 sm:space-y-6">
        <p className="text-center text-xs text-gray-500 sm:text-sm">
          <PERSON><PERSON><PERSON><PERSON> tin dùng bởi các công ty như
        </p>
        <div className="flex flex-wrap justify-around gap-4">
          <img src="https://placehold.co/100x40" alt="Logo công ty" className="h-6 opacity-50 grayscale transition-opacity duration-300 hover:opacity-75 sm:h-8" />
          <img src="https://placehold.co/100x40" alt="Logo công ty" className="h-6 opacity-50 grayscale transition-opacity duration-300 hover:opacity-75 sm:h-8" />
          <img src="https://placehold.co/100x40" alt="Logo công ty" className="h-6 opacity-50 grayscale transition-opacity duration-300 hover:opacity-75 sm:h-8" />
          <img src="https://placehold.co/100x40" alt="Logo công ty" className="h-6 opacity-50 grayscale transition-opacity duration-300 hover:opacity-75 sm:h-8" />
        </div>
      </div>
    </div>;
}
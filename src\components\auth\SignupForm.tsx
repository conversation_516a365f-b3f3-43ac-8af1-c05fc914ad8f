"use client";
import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Google } from "@/components/icons/Google";
import { useAuthForm } from "@/hooks/useAuthForm";
import { useGoogleAuth } from "@/hooks/useGoogleAuth";
import Link from "next/link";

export function SignupForm() {
  const {
    formData,
    formErrors,
    isLoading,
    error,
    success,
    handleInputChange,
    handleSignup,
  } = useAuthForm();

  const { signInWithGoogle, isLoading: googleLoading } = useGoogleAuth();

  return (
    <div className="flex min-h-screen w-full items-center justify-center p-4 lg:min-h-0 lg:p-8">
      <div className="w-full max-w-[440px] space-y-8 rounded-2xl bg-[#0F1128]/40 p-4 backdrop-blur-sm transition-all duration-500 hover:bg-[#0F1128]/50 sm:p-6 md:p-8">
        <div
          className="animate-fade-in-up space-y-2 text-center"
          style={{
            animationDelay: "0.1s",
          }}
        >
          <h1 className="bg-gradient-to-r from-white to-gray-400 bg-clip-text text-3xl font-bold tracking-tight text-transparent sm:text-4xl">
            Tạo tài khoản
          </h1>
          <p className="text-sm text-gray-400 sm:text-base">
            Bắt đầu hành trình của bạn với chúng tôi
          </p>
        </div>

        {/* Success/Error Messages */}
        {(error || success) && (
          <div
            className="animate-fade-in-up"
            style={{ animationDelay: "0.15s" }}
          >
            {error && (
              <div className="rounded-xl border border-red-500/20 bg-red-500/10 p-3 text-sm text-red-400">
                {error}
              </div>
            )}
            {success && (
              <div className="rounded-xl border border-green-500/20 bg-green-500/10 p-3 text-sm text-green-400">
                {success}
              </div>
            )}
          </div>
        )}

        <div
          className="animate-fade-in-up space-y-4 sm:space-y-6"
          style={{
            animationDelay: "0.2s",
          }}
        >
          <Button
            variant="outline"
            onClick={signInWithGoogle}
            disabled={isLoading || googleLoading}
            className="group relative w-full overflow-hidden rounded-xl border-[#2D2F53] bg-[#161837]/50 py-5 text-sm text-gray-300 transition-all duration-300 hover:border-[#3D4073] hover:bg-[#1A1D40]/50 hover:text-white disabled:cursor-not-allowed disabled:opacity-50 sm:py-6 sm:text-base"
          >
            <div className="relative z-10 flex items-center justify-center">
              <Google className="mr-2 h-4 w-4 transition-transform duration-300 group-hover:scale-110" />
              {googleLoading ? "Đang đăng ký..." : "Đăng ký với Google"}
            </div>
          </Button>
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-[#2D2F53]" />
            </div>
            <div className="relative flex justify-center text-xs sm:text-sm">
              <span className="bg-[#0F1128]/40 px-4 text-gray-500">
                Hoặc đăng ký với email
              </span>
            </div>
          </div>
        </div>
        <form
          onSubmit={handleSignup}
          className="animate-fade-in-up space-y-6"
          style={{
            animationDelay: "0.3s",
          }}
        >
          <div className="space-y-4 sm:space-y-5">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-1 sm:space-y-2">
                <label
                  htmlFor="firstName"
                  className="block text-sm font-medium text-gray-300"
                >
                  Họ
                </label>
                <Input
                  id="firstName"
                  name="firstName"
                  type="text"
                  required
                  value={formData.firstName || ""}
                  onChange={(e) =>
                    handleInputChange("firstName", e.target.value)
                  }
                  className={`mt-1 block w-full rounded-xl border-[#2D2F53] bg-[#161837]/50 text-white placeholder:text-gray-500 focus-within:ring-0 focus-within:ring-offset-0 hover:border-[#3D4073] focus:ring-0 focus:ring-offset-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 ${
                    formErrors.firstName ? "border-red-500/50" : ""
                  }`}
                  placeholder="Nguyễn"
                />
                {formErrors.firstName && (
                  <p className="text-sm text-red-400">{formErrors.firstName}</p>
                )}
              </div>
              <div className="space-y-1 sm:space-y-2">
                <label
                  htmlFor="lastName"
                  className="block text-sm font-medium text-gray-300"
                >
                  Tên
                </label>
                <Input
                  id="lastName"
                  name="lastName"
                  type="text"
                  required
                  value={formData.lastName || ""}
                  onChange={(e) =>
                    handleInputChange("lastName", e.target.value)
                  }
                  className={`mt-1 block w-full rounded-xl border-[#2D2F53] bg-[#161837]/50 text-white placeholder:text-gray-500 focus-within:ring-0 focus-within:ring-offset-0 hover:border-[#3D4073] focus:ring-0 focus:ring-offset-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 ${
                    formErrors.lastName ? "border-red-500/50" : ""
                  }`}
                  placeholder="Văn A"
                />
                {formErrors.lastName && (
                  <p className="text-sm text-red-400">{formErrors.lastName}</p>
                )}
              </div>
            </div>
            <div className="space-y-1 sm:space-y-2">
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-300"
              >
                Email
              </label>
              <Input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className={`mt-1 block w-full rounded-xl border-[#2D2F53] bg-[#161837]/50 text-white placeholder:text-gray-500 focus-within:ring-0 focus-within:ring-offset-0 hover:border-[#3D4073] focus:ring-0 focus:ring-offset-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 ${
                  formErrors.email ? "border-red-500/50" : ""
                }`}
                placeholder="<EMAIL>"
              />
              {formErrors.email && (
                <p className="text-sm text-red-400">{formErrors.email}</p>
              )}
            </div>
            <div className="space-y-1 sm:space-y-2">
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-300"
              >
                Mật khẩu
              </label>
              <Input
                id="password"
                name="password"
                type="password"
                required
                value={formData.password}
                onChange={(e) => handleInputChange("password", e.target.value)}
                className={`mt-1 block w-full rounded-xl border-[#2D2F53] bg-[#161837]/50 text-white placeholder:text-gray-500 focus-within:ring-0 focus-within:ring-offset-0 hover:border-[#3D4073] focus:ring-0 focus:ring-offset-0 focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0 ${
                  formErrors.password ? "border-red-500/50" : ""
                }`}
                placeholder="Tạo mật khẩu mới"
              />
              {formErrors.password && (
                <p className="text-sm text-red-400">{formErrors.password}</p>
              )}
            </div>
          </div>
          <Button
            type="submit"
            disabled={isLoading}
            className="group relative w-full overflow-hidden rounded-xl bg-gradient-to-r from-[#8B5CF6] to-[#7C3AED] py-5 text-sm text-white transition-all duration-300 hover:from-[#7C3AED] hover:to-[#6D28D9] disabled:cursor-not-allowed disabled:opacity-50 sm:py-6 sm:text-base"
          >
            <div className="relative z-10 flex items-center justify-center transition-transform duration-300 group-hover:scale-105">
              {isLoading ? "Đang đăng ký..." : "Đăng ký"}
            </div>
          </Button>
        </form>
        <div
          className="animate-fade-in-up"
          style={{
            animationDelay: "0.4s",
          }}
        >
          <p className="text-center text-xs text-gray-500 sm:text-sm">
            Bằng cách đăng ký, bạn đồng ý với{" "}
            <a
              href="#"
              className="text-[#8B5CF6] transition-colors duration-300 hover:text-[#7C3AED]"
            >
              Điều khoản Dịch vụ
            </a>{" "}
            và{" "}
            <a
              href="#"
              className="text-[#8B5CF6] transition-colors duration-300 hover:text-[#7C3AED]"
            >
              Chính sách Bảo mật
            </a>
          </p>
          <div className="flex items-center justify-center space-x-2 pt-2 text-sm sm:pt-3">
            <span className="text-gray-400">Đã có tài khoản?</span>
            <Link
              href="/login"
              className="font-medium text-[#8B5CF6] transition-colors duration-300 hover:text-[#7C3AED]"
            >
              Đăng nhập
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}




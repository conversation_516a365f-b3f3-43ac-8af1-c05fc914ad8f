"use client";

import { useState, useCallback } from "react";
import { createClient } from "@/lib/supabase/client";
import { useRouter } from "next/navigation";
import { AuthError } from "@supabase/supabase-js";

interface AuthData {
  email: string;
  password: string;
  name?: string;
}

interface AuthState {
  isLoading: boolean;
  error: string | null;
  success: string | null;
}

export function useAuth() {
  const [authState, setAuthState] = useState<AuthState>({
    isLoading: false,
    error: null,
    success: null,
  });

  const supabase = createClient();
  const router = useRouter();

  const login = useCallback(
    async (data: AuthData) => {
      setAuthState({ isLoading: true, error: null, success: null });

      try {
        const { error } = await supabase.auth.signInWithPassword({
          email: data.email,
          password: data.password,
        });

        if (error) {
          throw error;
        }

        setAuthState({
          isLoading: false,
          error: null,
          success: "<PERSON><PERSON>ng nhập thành công!",
        });

        // Redirect to dashboard
        router.push("/dashboard");
        router.refresh();
      } catch (error) {
        const message =
          error instanceof AuthError
            ? error.message
            : "Đăng nhập thất bại. Vui lòng kiểm tra thông tin đăng nhập.";
        setAuthState({
          isLoading: false,
          error: message,
          success: null,
        });
      }
    },
    [supabase.auth, router],
  );

  const signup = useCallback(
    async (data: AuthData) => {
      setAuthState({ isLoading: true, error: null, success: null });

      try {
        const { error } = await supabase.auth.signUp({
          email: data.email,
          password: data.password,
          options: {
            data: {
              name: data.name,
            },
          },
        });

        if (error) {
          throw error;
        }

        setAuthState({
          isLoading: false,
          error: null,
          success:
            "Tài khoản đã được tạo thành công! Vui lòng kiểm tra email để xác nhận tài khoản.",
        });
      } catch (error) {
        const message =
          error instanceof AuthError
            ? error.message
            : "Đăng ký thất bại. Vui lòng thử lại.";
        setAuthState({
          isLoading: false,
          error: message,
          success: null,
        });
      }
    },
    [supabase.auth],
  );

  const signOut = useCallback(async () => {
    setAuthState({ isLoading: true, error: null, success: null });

    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        throw error;
      }

      setAuthState({
        isLoading: false,
        error: null,
        success: "Successfully signed out!",
      });

      // Redirect to home
      router.push("/");
      router.refresh();
    } catch (error) {
      const message =
        error instanceof AuthError ? error.message : "Sign out failed.";
      setAuthState({
        isLoading: false,
        error: message,
        success: null,
      });
    }
  }, [supabase.auth, router]);

  const resetPassword = useCallback(
    async (email: string) => {
      setAuthState({ isLoading: true, error: null, success: null });

      try {
        const { error } = await supabase.auth.resetPasswordForEmail(email, {
          redirectTo: `${window.location.origin}/auth/update-password`,
        });

        if (error) {
          throw error;
        }

        setAuthState({
          isLoading: false,
          error: null,
          success: "Password reset email sent! Please check your inbox.",
        });
      } catch (error) {
        const message =
          error instanceof AuthError
            ? error.message
            : "Failed to send reset email.";
        setAuthState({
          isLoading: false,
          error: message,
          success: null,
        });
      }
    },
    [supabase.auth],
  );

  const updatePassword = useCallback(
    async (newPassword: string) => {
      setAuthState({ isLoading: true, error: null, success: null });

      try {
        const { error } = await supabase.auth.updateUser({
          password: newPassword,
        });

        if (error) {
          throw error;
        }

        setAuthState({
          isLoading: false,
          error: null,
          success: "Password updated successfully!",
        });

        // Redirect to dashboard
        router.push("/dashboard");
      } catch (error) {
        const message =
          error instanceof AuthError
            ? error.message
            : "Failed to update password.";
        setAuthState({
          isLoading: false,
          error: message,
          success: null,
        });
      }
    },
    [supabase.auth, router],
  );

  const clearMessages = useCallback(() => {
    setAuthState((prev) => ({ ...prev, error: null, success: null }));
  }, []);

  return {
    ...authState,
    login,
    signup,
    signOut,
    resetPassword,
    updatePassword,
    clearMessages,
  };
}

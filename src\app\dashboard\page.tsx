import { createClient } from "@/lib/supabase/server";
import { redirect } from "next/navigation";
import { LogoutButton } from "@/components/auth/LogoutButton";

export default async function DashboardPage() {
  const supabase = await createClient();

  const {
    data: { user },
  } = await supabase.auth.getUser();

  if (!user) {
    redirect("/login");
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="mx-auto max-w-7xl py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="overflow-hidden rounded-lg bg-white shadow">
            <div className="px-4 py-5 sm:p-6">
              <div className="mb-6 flex items-center justify-between">
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">
                    Dashboard
                  </h1>
                  <p className="mt-1 text-sm text-gray-600">
                    Welcome back, {user.email}!
                  </p>
                </div>
                <LogoutButton />
              </div>

              <div className="rounded-lg border-4 border-dashed border-gray-200 p-8">
                <div className="text-center">
                  <h3 className="mt-2 text-sm font-medium text-gray-900">
                    Protected Content
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    This page is only accessible to authenticated users.
                  </p>

                  <div className="mt-6 rounded-lg bg-gray-50 p-4">
                    <h4 className="mb-2 text-sm font-medium text-gray-900">
                      User Information:
                    </h4>
                    <div className="space-y-1 text-left text-sm text-gray-600">
                      <p>
                        <strong>Email:</strong> {user.email}
                      </p>
                      <p>
                        <strong>User ID:</strong> {user.id}
                      </p>
                      <p>
                        <strong>Created:</strong>{" "}
                        {new Date(user.created_at).toLocaleDateString()}
                      </p>
                      <p>
                        <strong>Last Sign In:</strong>{" "}
                        {user.last_sign_in_at
                          ? new Date(user.last_sign_in_at).toLocaleDateString()
                          : "N/A"}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

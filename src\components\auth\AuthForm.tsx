"use client";

import React, { useState } from "react";
import { LoginForm } from "./LoginForm";
import { SignupForm } from "./SignupForm";
import { Testimonials } from "./Testimonials";

export function AuthForm() {
  const [isLoginView, setIsLoginView] = useState(true);
  const [isAnimating, setIsAnimating] = useState(false);
  const [key, setKey] = useState(0); // Key to force re-render and reset form state

  const handleViewSwitch = (toLogin: boolean) => {
    if (isAnimating) return;
    setIsAnimating(true);
    setTimeout(() => {
      setIsLoginView(toLogin);
      setKey((prev) => prev + 1); // Force re-render to reset form state
      setIsAnimating(false);
    }, 300);
  };

  return (
    <main className="min-h-screen w-full bg-[#0A0B1A] bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-[#0A0B1A] via-[#0A0B1A] to-[#0F1128]">
      <div className="container mx-auto grid min-h-screen lg:grid-cols-2">
        <div className="relative flex items-center justify-center">
          <div
            className={`w-full transition-opacity duration-300 ease-in-out ${
              isAnimating ? "opacity-0" : "opacity-100"
            }`}
          >
            {isLoginView ? (
              <LoginForm
                key={`login-${key}`}
                onSwitchToSignup={() => handleViewSwitch(false)}
              />
            ) : (
              <SignupForm
                key={`signup-${key}`}
                onSwitchToLogin={() => handleViewSwitch(true)}
              />
            )}
          </div>
        </div>
        <Testimonials />
      </div>
    </main>
  );
}

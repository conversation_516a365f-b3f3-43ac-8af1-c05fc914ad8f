"use client";

import { useState, useCallback } from "react";
import { useAuth } from "./useAuth";

interface FormData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  rememberMe?: boolean;
}

interface FormErrors {
  email?: string;
  password?: string;
  firstName?: string;
  lastName?: string;
}

export function useAuthForm() {
  const [formData, setFormData] = useState<FormData>({
    email: "",
    password: "",
    firstName: "",
    lastName: "",
    rememberMe: false,
  });

  const [formErrors, setFormErrors] = useState<FormErrors>({});
  const { login, signup, isLoading, error, success, clearMessages } = useAuth();

  const validateEmail = useCallback((email: string): string | undefined => {
    if (!email) return "Email là bắt buộc";
    if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      return "<PERSON><PERSON> không hợp lệ";
    }
    return undefined;
  }, []);

  const validatePassword = useCallback((password: string): string | undefined => {
    if (!password) return "Mật khẩu là bắt buộc";
    if (password.length < 6) return "Mật khẩu phải có ít nhất 6 ký tự";
    return undefined;
  }, []);

  const validateName = useCallback((name: string): string | undefined => {
    if (!name) return "Tên là bắt buộc";
    if (name.length < 2) return "Tên phải có ít nhất 2 ký tự";
    return undefined;
  }, []);

  const handleInputChange = useCallback((name: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear error for this field when user starts typing
    if (formErrors[name as keyof FormErrors]) {
      setFormErrors(prev => ({ ...prev, [name]: undefined }));
    }
    
    // Clear auth messages when user interacts with form
    if (error || success) {
      clearMessages();
    }
  }, [formErrors, error, success, clearMessages]);

  const validateForm = useCallback((isSignup: boolean = false): boolean => {
    const errors: FormErrors = {};

    const emailError = validateEmail(formData.email);
    if (emailError) errors.email = emailError;

    const passwordError = validatePassword(formData.password);
    if (passwordError) errors.password = passwordError;

    if (isSignup) {
      const firstNameError = validateName(formData.firstName || "");
      if (firstNameError) errors.firstName = firstNameError;

      const lastNameError = validateName(formData.lastName || "");
      if (lastNameError) errors.lastName = lastNameError;
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  }, [formData, validateEmail, validatePassword, validateName]);

  const handleLogin = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm(false)) return;

    await login({
      email: formData.email,
      password: formData.password,
    });
  }, [formData, validateForm, login]);

  const handleSignup = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm(true)) return;

    await signup({
      email: formData.email,
      password: formData.password,
      name: `${formData.firstName} ${formData.lastName}`.trim(),
    });
  }, [formData, validateForm, signup]);

  const resetForm = useCallback(() => {
    setFormData({
      email: "",
      password: "",
      firstName: "",
      lastName: "",
      rememberMe: false,
    });
    setFormErrors({});
    clearMessages();
  }, [clearMessages]);

  return {
    formData,
    formErrors,
    isLoading,
    error,
    success,
    handleInputChange,
    handleLogin,
    handleSignup,
    resetForm,
  };
} 